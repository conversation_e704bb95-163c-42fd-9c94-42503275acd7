import PublicClientApplication from "react-native-msal";
import { msalConfig } from "../../msalConfig";
import * as Keychain from "react-native-keychain";

export const storeAppleUserInfo = async (userId, data) => {
  await Keychain.setGenericPassword(userId, JSON.stringify(data));
};

export const getStoredAppleUserInfo = async (userId) => {
  const credentials = await Keychain.getGenericPassword();
  if (credentials && credentials.username === userId) {
    return JSON.parse(credentials.password);
  }
  return null;
};

const pca = new PublicClientApplication(msalConfig);
const scopes = ["Contacts.ReadWrite", "People.Read"];

let initialized = false;

const ensureInitialized = async () => {
  if (!initialized) {
    await pca.init();
    initialized = true;
  }
};

export const signInAndGetToken = async () => {
  try {
    await ensureInitialized();
    const result = await pca.acquireToken({ scopes });
    console.log("MSAL Sign-In Result:", result);

    return result.accessToken;
  } catch (error) {
    console.error("MSAL Sign-In Error:", error);
    throw error;
  }
};

// Helper function to fetch contact photo
export const fetchMicrosoftContactPhoto = async (contactId, accessToken) => {
  try {
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/me/contacts/${contactId}/photo/$value`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (response.ok) {
      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });
    }
    return null;
  } catch (error) {
    console.log(`No photo available for contact ${contactId}`);
    return null;
  }
};

export const fetchMicrosoftContacts = async (
  accessToken,
  includePhotos = false
) => {
  try {
    let allContacts = [];
    let nextUrl = "https://graph.microsoft.com/v1.0/me/contacts?$top=999";

    do {
      const response = await fetch(nextUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.value && data.value.length > 0) {
        allContacts = allContacts.concat(data.value);
      }

      // Check for next page
      nextUrl = data["@odata.nextLink"] || null;
    } while (nextUrl);

    console.log(`Fetched ${allContacts.length} Microsoft Contacts total`);

    // Optionally fetch photos for each contact
    if (includePhotos && allContacts.length > 0) {
      console.log("Fetching profile photos for Microsoft contacts...");
      const contactsWithPhotos = await Promise.all(
        allContacts.map(async (contact) => {
          if (contact.id) {
            const photoUrl = await fetchMicrosoftContactPhoto(
              contact.id,
              accessToken
            );
            return { ...contact, photoUrl };
          }
          return contact;
        })
      );
      console.log("Completed fetching profile photos");
      return contactsWithPhotos;
    }

    return allContacts;
  } catch (error) {
    console.error("Error fetching contacts from Microsoft Graph:", error);
    throw error;
  }
};

export const updateMicrosoftContact = async (
  contactId,
  updatedFields,
  accessToken
) => {
  try {
    const response = await fetch(
      `https://graph.microsoft.com/v1.0/me/contacts/${contactId}`,
      {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedFields),
      }
    );
    if (!response.ok) {
      const errData = await response.json();
      throw new Error(JSON.stringify(errData));
    }
    const updatedContact = await response.json();
    console.log("Updated Microsoft Contact:", updatedContact);

    return updatedContact;
  } catch (error) {
    console.error("Error updating Microsoft contact:", error);
    throw error;
  }
};

// Format a single contact based on its source
export const formatContact = (contact, source) => {
  // console.log("format contact funtion ", JSON.stringify(contact, null, 2));
  if (source === "google") {
    return {
      id: contact?.resourceName,
      firstName: contact.names?.[0]?.givenName || "",
      middleName: contact.names?.[0]?.middleName || "",
      lastName: contact.names?.[0]?.familyName || "",
      displayName: contact.names?.[0]?.displayName || "",
      source: "google",
      emails: contact.emailAddresses
        ? contact.emailAddresses.map((email) => ({
            address: email.value || "",
            type: email.type || "other",
          }))
        : [],
      phoneNumbers: contact.phoneNumbers
        ? contact.phoneNumbers.map((phone) => ({
            number: phone.value || "",
            countryCode: phone.countryCode || "",
            type: phone.label || "",
          }))
        : [], // Assuming no phone numbers are provided in Google data
      languages: [], // Assuming no languages are provided in Google data
      hobbies: [], // Assuming no hobbies are provided in Google data
      addresses_home: {}, // Assuming no address is provided in Google data
      company: {}, // Assuming no company information is provided in Google data
      profile_image: contact.photos?.[0]?.url || "",
    };
  } else if (source === "local_device") {
    return {
      id: contact?.recordID,
      firstName: contact.givenName || "",
      middleName: contact.middleName || "",
      lastName: contact.familyName || "",
      source: "local",
      emails: contact.emailAddresses
        ? contact.emailAddresses.map((email) => ({
            address: email.email || "",
            type: email.label || "other",
          }))
        : [],
      phoneNumbers: contact.phoneNumbers
        ? contact.phoneNumbers.map((phone) => ({
            number: phone.number || "",
            countryCode: phone.countryCode || "",
            type: phone.label || "",
          }))
        : [],
      languages: [], // Assuming no languages are provided in local data
      hobbies: [], // Assuming no hobbies are provided in local data
      addresses_home: contact.postalAddresses?.[0] || {},
      company: {}, // Assuming no company information is provided in local data
      profile_image: contact.thumbnailPath || "",
    };
  } else if (source === "microsoft") {
    // Microsoft Graph API contact mapping
    return {
      id:
        contact.id ||
        contact.Id ||
        contact.emailAddresses?.[0]?.address ||
        contact.displayName,
      firstName: contact.givenName || contact.firstName || "",
      middleName: contact.middleName || "",
      lastName: contact.surname || contact.lastName || "",
      displayName: contact.displayName || "",
      source: "microsoft",
      emails: Array.isArray(contact.emailAddresses)
        ? contact.emailAddresses.map((email) => ({
            address: email.address || email.email || "",
            type: "other",
          }))
        : [],
      phoneNumbers: [
        ...(Array.isArray(contact.mobilePhone)
          ? contact.mobilePhone
          : contact.mobilePhone
          ? [contact.mobilePhone]
          : []),
        ...(Array.isArray(contact.businessPhones)
          ? contact.businessPhones
          : contact.businessPhones
          ? [contact.businessPhones]
          : []),
        ...(Array.isArray(contact.homePhones)
          ? contact.homePhones
          : contact.homePhones
          ? [contact.homePhones]
          : []),
      ]
        .filter(Boolean)
        .map((number) => ({
          number: typeof number === "string" ? number : "",
          countryCode: "",
          type: "other",
        })),
      languages: [],
      hobbies: [],
      addresses_home: contact.homeAddress || {},
      company: contact.companyName ? { name: contact.companyName } : {},
      department: contact.department || "",
      jobTitle: contact.jobTitle || "",
      profile_image: contact.photoUrl || "",
    };
  }
  return null;
};

// Group contacts by first letter of their first name, with sorting order based on sortOrder param
export const groupContactsByAlphabet = (contacts, sortOrder = "az") => {
  if (!Array.isArray(contacts) || contacts.length === 0) {
    return [];
  }

  // If not az/za, return flat list (no grouping)
  if (sortOrder !== "az" && sortOrder !== "za") {
    return contacts;
  }

  // Group contacts by first letter
  const groupedContacts = contacts.reduce((acc, contact) => {
    if (!contact) return acc;

    const firstLetter =
      (contact.firstName ? contact.firstName.charAt(0).toUpperCase() : null) ||
      "#";

    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }

    acc[firstLetter].push(contact);
    return acc;
  }, {});
  // Sort contacts within each group alphabetically by firstName, middleName, then lastName only if sortOrder is 'az' or 'za'
  if (sortOrder === "az" || sortOrder === "za") {
    Object.keys(groupedContacts).forEach((letter) => {
      groupedContacts[letter].sort((a, b) => {
        const nameA =
          (a.firstName || "") + (a.middleName || "") + (a.lastName || "");
        const nameB =
          (b.firstName || "") + (b.middleName || "") + (b.lastName || "");
        return sortOrder === "az"
          ? nameA.localeCompare(nameB)
          : nameB.localeCompare(nameA);
      });
    });
  }
  // Convert grouped contacts into the format expected by SectionList
  let sortedKeys = Object.keys(groupedContacts);
  if (sortOrder === "az" || sortOrder === "za") {
    sortedKeys = sortedKeys.sort((a, b) => {
      if (a === "#") return 1;
      if (b === "#") return -1;
      return sortOrder === "az" ? a.localeCompare(b) : b.localeCompare(a);
    });
  }
  return sortedKeys.map((letter) => ({
    title: letter,
    data: groupedContacts[letter],
  }));
};

// Format and group contacts in one step
export const formatContactData = (data, source) => {
  if (!Array.isArray(data)) {
    console.warn("formatContactData: data is not an array", data);
    return [];
  }

  // Process all contacts at once
  const formattedContacts = data
    .map((contact) => formatContact(contact, source))
    .filter(Boolean);
  return groupContactsByAlphabet(formattedContacts);
};

export function formatDate(dateString) {
  const date = new Date(dateString);

  // Get month name
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  const month = monthNames[date.getMonth()];

  // Get day and year
  const day = date.getDate();
  const year = date.getFullYear();

  // Format as "Month, Day, Year"
  return `${month} ${day}, ${year}`;
}
